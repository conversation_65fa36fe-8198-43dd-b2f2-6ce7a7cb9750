// pages/compress/compress.js
Page({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,
    
    // 文件状态
    fileSelected: false,
    compressing: false,
    compressed: false,
    
    // 文件信息
    fileName: '',
    fileSize: '',
    filePath: '',

    // 多文件信息
    selectedFiles: [],

    // 压缩选项
    compressionLevel: 5,
    targetFileSize: '', // 目标文件大小，如 "10MB", "5MB"

    // 自定义弹窗相关
    showTargetSizeModal: false,
    inputTargetSize: '',
    inputFocus: false,

    // 预估效果
    showEstimate: false,
    originalSizeText: '',
    estimatedSize: '',
    estimatedRatio: '',
    
    // 压缩进度
    progressPercent: 0,
    
    // 压缩结果
    originalSize: '',
    compressedSize: '',
    compressionRatio: 0,
    savedSize: '',
    compressedFilePath: ''
  },

  onLoad(options) {
    this.initNavbar()
  },

  // 初始化导航栏
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight
    const navbarHeight = statusBarHeight + 44

    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    const screenWidth = systemInfo.screenWidth
    const rpxRatio = 750 / screenWidth
    const menuButtonTopRpx = (menuButtonInfo.top - statusBarHeight + 1) * rpxRatio
    const menuButtonHeightRpx = menuButtonInfo.height * rpxRatio

    this.setData({
      statusBarHeight,
      navbarHeight,
      menuButtonTopRpx,
      menuButtonHeightRpx
    })
  },

  // 返回按钮
  onBackClick() {
    wx.navigateBack()
  },

  // 选择文件
  onSelectFile() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        // 处理所有选择的文件
        const files = res.tempFiles.map((file, index) => ({
          id: Date.now() + index,
          name: file.name,
          size: this.formatFileSize(file.size),
          path: file.path,
          originalSize: file.size,
          compressedSize: null
        }))

        this.setData({
          selectedFiles: files,
          fileSelected: true
        })

        // 如果只选择了一个文件，保持原有的单文件显示逻辑
        if (files.length === 1) {
          this.handleFileSelection(res.tempFiles[0])
        }
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },



  // 设置目标文件大小
  onSettings() {
    this.setData({
      showTargetSizeModal: true,
      inputTargetSize: this.data.targetFileSize || '',
      inputFocus: true
    })
  },

  // 隐藏目标大小弹窗
  hideTargetSizeModal() {
    this.setData({
      showTargetSizeModal: false,
      inputTargetSize: '',
      inputFocus: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，阻止点击弹窗内容时关闭弹窗
  },

  // 输入目标大小
  onTargetSizeInput(e) {
    console.log('输入事件触发，值:', e.detail.value)
    this.setData({
      inputTargetSize: e.detail.value
    })
    console.log('设置后的值:', this.data.inputTargetSize)
  },

  // 确认设置目标大小
  confirmTargetSize() {
    const targetSize = this.data.inputTargetSize.trim()

    console.log('输入的目标大小:', targetSize)
    console.log('验证结果:', this.validateFileSize(targetSize))

    if (!targetSize) {
      wx.showToast({
        title: '请输入目标文件大小',
        icon: 'none'
      })
      return
    }

    // 验证输入格式
    if (this.validateFileSize(targetSize)) {
      this.setData({
        targetFileSize: targetSize,
        showTargetSizeModal: false,
        inputTargetSize: '',
        inputFocus: false
      })

      wx.showToast({
        title: `目标大小已设置为 ${targetSize}`,
        icon: 'success'
      })

      // 重新计算预估效果
      this.calculateEstimateFromData()
    } else {
      wx.showToast({
        title: `格式错误，请输入如：10MB（当前输入：${targetSize}）`,
        icon: 'none'
      })
    }
  },

  // 验证文件大小格式
  validateFileSize(sizeStr) {
    // 支持格式：10MB, 5.5MB, 2GB, 500KB, 10M, 5G, 500K
    const regex = /^\d+(\.\d+)?(MB|GB|KB|M|G|K)$/i
    const result = regex.test(sizeStr)
    console.log(`验证 "${sizeStr}": ${result}`)
    return result
  },



  // 压缩强度滑块变化
  onCompressionLevelChange(e) {
    this.setData({
      compressionLevel: e.detail.value
    })
    this.calculateEstimateFromData()
  },

  // 开始压缩
  onStartCompress() {
    if (!this.data.filePath) {
      wx.showToast({
        title: '请先选择文件',
        icon: 'none'
      })
      return
    }

    this.setData({
      compressing: true,
      progressPercent: 0
    })

    // 模拟压缩进度
    this.simulateCompress()
  },

  // 模拟压缩过程
  simulateCompress() {
    let progress = 0
    const timer = setInterval(() => {
      progress += Math.random() * 15 + 5
      if (progress >= 100) {
        progress = 100
        clearInterval(timer)
        
        // 压缩完成
        setTimeout(() => {
          this.onCompressComplete()
        }, 500)
      }
      
      this.setData({
        progressPercent: Math.floor(progress)
      })
    }, 200)
  },

  // 压缩完成
  onCompressComplete() {
    // 模拟压缩结果数据
    const originalSizeBytes = this.parseFileSize(this.data.fileSize)
    const { compressionLevel, imageQuality, targetFileSize } = this.data

    let compressedSizeBytes
    let compressionRatio

    // 优先级1: 如果设置了目标文件大小，使用目标大小
    if (targetFileSize && targetFileSize.trim()) {
      const targetBytes = this.parseFileSize(targetFileSize)

      if (targetBytes > 0 && targetBytes < originalSizeBytes) {
        compressedSizeBytes = targetBytes
        const savedBytes = originalSizeBytes - compressedSizeBytes
        compressionRatio = Math.floor((savedBytes / originalSizeBytes) * 100)
      } else {
        // 目标大小无效，回退到压缩级别模式
        const baseCompressionRate = 0.9 - (compressionLevel - 1) * 0.067
        const qualityFactor = 1 - (100 - imageQuality) * 0.005
        const finalCompressionRate = baseCompressionRate * qualityFactor
        compressedSizeBytes = Math.floor(originalSizeBytes * finalCompressionRate)
        compressionRatio = Math.floor((1 - finalCompressionRate) * 100)
      }
    } else {
      // 优先级2: 使用压缩级别参数
      const baseCompressionRate = 0.9 - (compressionLevel - 1) * 0.067
      const qualityFactor = 1 - (100 - imageQuality) * 0.005
      const finalCompressionRate = baseCompressionRate * qualityFactor
      compressedSizeBytes = Math.floor(originalSizeBytes * finalCompressionRate)
      compressionRatio = Math.floor((1 - finalCompressionRate) * 100)
    }

    const savedBytes = originalSizeBytes - compressedSizeBytes

    this.setData({
      compressing: false,
      compressed: true,
      originalSize: this.data.fileSize,
      compressedSize: this.formatFileSize(compressedSizeBytes),
      compressionRatio: compressionRatio,
      savedSize: this.formatFileSize(savedBytes),
      compressedFilePath: this.data.filePath // 实际应用中这里应该是压缩后的文件路径
    })

    wx.showToast({
      title: '压缩完成！',
      icon: 'success'
    })
  },

  // 压缩其他文件
  onCompressAnother() {
    this.setData({
      fileSelected: false,
      compressing: false,
      compressed: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      progressPercent: 0,
      selectedQuality: 'medium'
    })
  },

  // 下载文件
  onDownload() {
    if (!this.data.compressedFilePath) {
      wx.showToast({
        title: '没有可下载的文件',
        icon: 'none'
      })
      return
    }

    // 实际应用中这里应该实现文件下载逻辑
    wx.showModal({
      title: '下载功能',
      content: '下载功能需要后端支持，当前为演示版本',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 解析文件大小字符串为字节数
  parseFileSize(sizeStr) {
    const units = {
      'B': 1,
      'KB': 1024, 'K': 1024,
      'MB': 1024*1024, 'M': 1024*1024,
      'GB': 1024*1024*1024, 'G': 1024*1024*1024
    }
    const match = sizeStr.match(/^([\d.]+)\s*(\w+)$/i)
    if (match) {
      const value = parseFloat(match[1])
      const unit = match[2].toUpperCase()
      return Math.floor(value * (units[unit] || 1))
    }
    return 0
  },

  // 切换灰度选项
  onToggleGrayscale(e) {
    this.setData({
      enableGrayscale: e.detail.value
    })
    this.calculateEstimateFromData()
  },



  // 从当前数据计算预估效果
  calculateEstimateFromData() {
    const originalSizeBytes = this.parseFileSize(this.data.fileSize)
    this.calculateEstimate(originalSizeBytes)
  },

  // 计算预估效果
  calculateEstimate(originalBytes) {
    const { compressionLevel, imageQuality, targetFileSize } = this.data

    let estimatedBytes
    let compressionRatio

    // 优先级1: 如果用户设置了目标文件大小，优先使用目标大小
    if (targetFileSize && targetFileSize.trim()) {
      const targetBytes = this.parseFileSize(targetFileSize)

      if (targetBytes > 0 && targetBytes < originalBytes) {
        estimatedBytes = targetBytes
        const savedBytes = originalBytes - estimatedBytes
        compressionRatio = ((savedBytes / originalBytes) * 100).toFixed(1)

        this.setData({
          estimatedSize: this.formatFileSize(estimatedBytes),
          estimatedRatio: `${compressionRatio}%`,
          compressionMode: 'target' // 标记当前使用目标大小模式
        })
        return
      } else if (targetBytes >= originalBytes) {
        // 目标大小大于原文件，显示提示
        wx.showToast({
          title: '目标大小不能大于原文件',
          icon: 'none'
        })
        // 清除无效的目标大小设置
        this.setData({
          targetFileSize: ''
        })
      }
    }

    // 优先级2: 使用压缩强度参数
    // 根据压缩强度计算基础压缩率 (1-9级别)
    // 级别1: 保留90%, 级别9: 保留30%
    const baseCompressionRate = 0.9 - (compressionLevel - 1) * 0.067

    // 根据图片质量调整压缩率
    // 质量100%: 不额外压缩, 质量10%: 额外压缩50%
    const qualityFactor = 1 - (100 - imageQuality) * 0.005

    // 综合压缩率
    const compressionRate = baseCompressionRate * qualityFactor

    estimatedBytes = originalBytes * compressionRate
    const savedBytes = originalBytes - estimatedBytes
    compressionRatio = ((savedBytes / originalBytes) * 100).toFixed(1)

    this.setData({
      estimatedSize: this.formatFileSize(estimatedBytes),
      estimatedRatio: `${compressionRatio}%`,
      compressionMode: 'level' // 标记当前使用压缩级别模式
    })
  },

  // 打开网页版
  openWebVersion() {
    wx.setClipboardData({
      data: 'https://your-website.com/pdf-compress',
      success: () => {
        wx.showToast({
          title: '链接已复制，请在浏览器中打开',
          icon: 'none',
          duration: 3000
        })
      }
    })
  },

  // 底部选择PDF按钮
  onSelectPDF() {
    wx.showActionSheet({
      itemList: ['从文件选择', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从文件选择
          this.onSelectFile()
        } else if (res.tapIndex === 1) {
          // 从聊天记录选择
          this.selectFromChat()
        }
      }
    })
  },

  // 从聊天记录选择
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        // 处理所有选择的文件
        const files = res.tempFiles.map((file, index) => ({
          id: Date.now() + index,
          name: file.name,
          size: this.formatFileSize(file.size),
          path: file.path,
          originalSize: file.size,
          compressedSize: null
        }))

        this.setData({
          selectedFiles: files,
          fileSelected: true
        })

        // 如果只选择了一个文件，保持原有的单文件显示逻辑
        if (files.length === 1) {
          this.handleFileSelection(res.tempFiles[0])
        }
      },
      fail: (err) => {
        console.error('从聊天记录选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },

  // 统一处理文件选择
  handleFileSelection(file) {
    // 检查文件大小 (100MB限制)
    const sizeMB = file.size / (1024 * 1024)
    if (sizeMB > 100) {
      wx.showModal({
        title: '文件过大',
        content: `文件大小为${sizeMB.toFixed(1)}MB，超过100MB限制。`,
        showCancel: false
      })
      return
    }

    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      wx.showToast({
        title: '请选择PDF格式文件',
        icon: 'none'
      })
      return
    }

    this.setData({
      fileSelected: true,
      fileName: file.name,
      fileSize: this.formatFileSize(file.size),
      filePath: file.path,
      originalSizeText: this.formatFileSize(file.size),
      showEstimate: true
    })

    // 计算预估效果
    this.calculateEstimate(file.size)
  },

  // 底部下载PDF按钮
  onDownloadPDF() {
    if (!this.data.compressed) {
      wx.showToast({
        title: '请先完成PDF压缩',
        icon: 'none'
      })
      return
    }

    // 执行下载逻辑
    this.onDownload()
  },

  onShareAppMessage() {
    return {
      title: 'PDF智能压缩工具 - 基于Stirling-PDF引擎',
      path: '/pages/compress/compress'
    }
  }
})
