// pages/pdf-to-word/pdf-to-word.js
Page({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,
    
    // 文件状态
    fileSelected: false,
    converting: false,
    converted: false,
    
    // 文件信息
    fileName: '',
    fileSize: '',
    filePath: '',

    // 多文件信息
    selectedFiles: [],

    // 转换选项
    quality: 'medium', // high, medium, fast
    qualityText: '标准',

    // 转换进度
    progressPercent: 0,
    
    // 转换结果
    originalSize: '',
    convertedSize: '',
    convertedFilePath: ''
  },

  onLoad(options) {
    this.initNavbar()
  },

  // 初始化导航栏
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync()
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navbarHeight: systemInfo.statusBarHeight + 44,
      menuButtonTopRpx: (menuButtonInfo.top - systemInfo.statusBarHeight) * 2,
      menuButtonHeightRpx: menuButtonInfo.height * 2
    })
  },

  // 返回按钮
  onBackClick() {
    wx.navigateBack()
  },

  // 选择文件
  onSelectFile() {
    wx.chooseMessageFile({
      count: 10,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        console.log('选择的文件:', res.tempFiles)
        
        if (res.tempFiles.length === 1) {
          // 单文件处理
          const file = res.tempFiles[0]
          this.setData({
            fileSelected: true,
            fileName: file.name,
            fileSize: this.formatFileSize(file.size),
            filePath: file.path,
            selectedFiles: [{
              id: Date.now(),
              name: file.name,
              size: this.formatFileSize(file.size),
              path: file.path,
              convertedSize: ''
            }]
          })
        } else {
          // 多文件处理
          const files = res.tempFiles.map((file, index) => ({
            id: Date.now() + index,
            name: file.name,
            size: this.formatFileSize(file.size),
            path: file.path,
            convertedSize: ''
          }))
          
          this.setData({
            fileSelected: true,
            selectedFiles: files,
            fileName: `${files.length}个文件`,
            fileSize: this.getTotalSize(res.tempFiles)
          })
        }
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'error'
        })
      }
    })
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 计算总大小
  getTotalSize(files) {
    const totalBytes = files.reduce((sum, file) => sum + file.size, 0)
    return this.formatFileSize(totalBytes)
  },

  // 转换质量选择
  onQualityChange(e) {
    const quality = e.currentTarget.dataset.quality
    let qualityText = ''
    
    switch(quality) {
      case 'high':
        qualityText = '高质量'
        break
      case 'medium':
        qualityText = '标准'
        break
      case 'fast':
        qualityText = '快速'
        break
    }
    
    this.setData({
      quality,
      qualityText
    })
  },

  // 开始转换
  onStartConvert() {
    this.setData({
      converting: true,
      progressPercent: 0
    })

    // 模拟转换进度
    this.simulateConversion()
  },

  // 模拟转换过程
  simulateConversion() {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 15
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
        
        // 转换完成
        setTimeout(() => {
          this.setData({
            converting: false,
            converted: true,
            progressPercent: 100,
            convertedSize: this.generateConvertedSize(),
            convertedFilePath: '/temp/converted.docx'
          })
        }, 500)
      }
      
      this.setData({
        progressPercent: Math.floor(progress)
      })
    }, 200)
  },

  // 生成转换后的文件大小（模拟）
  generateConvertedSize() {
    // 简单模拟：Word文档通常比PDF稍大
    const originalBytes = this.data.selectedFiles[0]?.size || this.data.fileSize
    // 这里只是示例，实际应该根据真实转换结果
    return this.data.fileSize
  },

  // 下载Word文档
  onDownloadWord() {
    if (!this.data.converted) {
      wx.showToast({
        title: '请先完成PDF转换',
        icon: 'none'
      })
      return
    }

    wx.showToast({
      title: '开始下载Word文档',
      icon: 'success'
    })

    // 这里应该实现真实的下载逻辑
    console.log('下载文件:', this.data.convertedFilePath)
  },

  // 转换其他文件
  onConvertAnother() {
    this.setData({
      fileSelected: false,
      converting: false,
      converted: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      selectedFiles: [],
      progressPercent: 0,
      convertedSize: '',
      convertedFilePath: '',
      quality: 'medium',
      qualityText: '标准'
    })
  },

  // 底部选择PDF按钮
  onSelectPDF() {
    wx.showActionSheet({
      itemList: ['从文件选择', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从文件选择
          this.onSelectFile()
        } else if (res.tapIndex === 1) {
          // 从聊天记录选择
          this.selectFromChat()
        }
      }
    })
  },

  // 从聊天记录选择
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        // 处理所有选择的文件
        const files = res.tempFiles.map((file, index) => ({
          id: Date.now() + index,
          name: file.name,
          size: this.formatFileSize(file.size),
          path: file.path,
          originalSize: file.size,
          convertedSize: null
        }))

        this.setData({
          fileSelected: true,
          selectedFiles: files,
          fileName: files.length === 1 ? files[0].name : `${files.length}个文件`,
          fileSize: files.length === 1 ? files[0].size : this.getTotalSize(res.tempFiles)
        })

        console.log('从聊天记录选择的文件:', files)
      },
      fail: (err) => {
        console.error('从聊天记录选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'error'
        })
      }
    })
  },

  // 设置按钮（预留）
  onSettings() {
    wx.showToast({
      title: '设置功能开发中',
      icon: 'none'
    })
  },

  onShow() {
    console.log('页面显示')
  },

  onReady() {
    console.log('页面初次渲染完成')
  },

  onHide() {
    console.log('页面隐藏')
  },

  onUnload() {
    console.log('页面卸载')
  }
})
