<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="waterGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4FC3F7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#29B6F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0288D1;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="dropShadow" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </radialGradient>
  </defs>

  <!-- 主水滴形状 -->
  <path d="M12 4C12 4 6 10 6 15C6 18.314 8.686 21 12 21C15.314 21 18 18.314 18 15C18 10 12 4 12 4Z"
        fill="url(#waterGrad)"
        stroke="#0277BD"
        stroke-width="1"/>

  <!-- 水滴高光效果 -->
  <ellipse cx="10" cy="12" rx="2" ry="3" fill="url(#dropShadow)" opacity="0.6"/>

  <!-- 小水滴装饰 -->
  <circle cx="16" cy="8" r="1.5" fill="url(#waterGrad)" opacity="0.7"/>
  <circle cx="16" cy="8" r="0.8" fill="url(#dropShadow)" opacity="0.5"/>

  <circle cx="8" cy="6" r="1" fill="url(#waterGrad)" opacity="0.5"/>
  <circle cx="8" cy="6" r="0.5" fill="url(#dropShadow)" opacity="0.4"/>
</svg>
