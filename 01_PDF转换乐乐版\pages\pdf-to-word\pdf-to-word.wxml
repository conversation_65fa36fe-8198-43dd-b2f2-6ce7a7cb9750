<!--pages/pdf-to-word/pdf-to-word.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF转Word</view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectFile">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/convert.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择或拖拽文件到此处</view>
        <view class="upload-tips">支持PDF格式，转换为Word文档</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !converting}}">
    <!-- 多文件显示 -->
    <view wx:if="{{selectedFiles.length > 1}}">
      <view class="file-info-card" wx:for="{{selectedFiles}}" wx:key="id">
        <image class="file-icon" src="/images/pdf-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{item.name}}</view>
          <view class="file-size-info">
            <text class="size-label">原文件：</text>
            <text class="size-value">{{item.size}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">转换后：</text>
            <text class="size-value {{item.convertedSize ? 'converted' : 'placeholder'}}">{{item.convertedSize || 'XXX KB'}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>

    <!-- 单文件显示 -->
    <view wx:if="{{selectedFiles.length <= 1}}">
      <view class="file-info-card">
        <image class="file-icon" src="/images/pdf-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{fileName}}</view>
          <view class="file-size-info">
            <text class="size-label">原文件：</text>
            <text class="size-value">{{fileSize}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">转换后：</text>
            <text class="size-value {{convertedSize ? 'converted' : 'placeholder'}}">{{convertedSize || 'XXX KB'}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>

  </view>

  <!-- 底部固定区域 -->
  <view class="footer-area" wx:if="{{fileSelected && !converting && !converted}}">
    <!-- 转换设置 -->
    <view class="convert-settings">
      <!-- 转换质量选择 -->
      <view class="setting-item">
        <view class="setting-header">
          <text class="setting-label">转换质量</text>
          <text class="setting-value">{{qualityText}}</text>
        </view>
        <view class="quality-options">
          <view class="quality-option {{quality === 'high' ? 'active' : ''}}" bindtap="onQualityChange" data-quality="high">
            <text class="quality-text">高质量</text>
            <text class="quality-desc">保持原始格式</text>
          </view>
          <view class="quality-option {{quality === 'medium' ? 'active' : ''}}" bindtap="onQualityChange" data-quality="medium">
            <text class="quality-text">标准</text>
            <text class="quality-desc">平衡质量与速度</text>
          </view>
          <view class="quality-option {{quality === 'fast' ? 'active' : ''}}" bindtap="onQualityChange" data-quality="fast">
            <text class="quality-text">快速</text>
            <text class="quality-desc">快速转换</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 转换进行中 -->
  <view class="converting-section" wx:if="{{converting}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/convert.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在转换为Word...</view>
        <view class="progress-subtitle">请稍候，正在为您转换文档格式</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 转换完成 -->
  <view class="result-section" wx:if="{{converted}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/document.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">转换完成！</view>
        <view class="result-subtitle">PDF已成功转换为Word文档</view>
        <view class="result-info">
          <text class="info-label">原文件：</text>
          <text class="info-value">{{fileSize}}</text>
          <text class="info-separator">　</text>
          <text class="info-label">转换后：</text>
          <text class="info-value">{{convertedSize}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <!-- 开始转换按钮 -->
    <view class="action-btn convert-btn" wx:if="{{fileSelected && !converting && !converted}}" bindtap="onStartConvert">
      <text class="btn-text">开始转换</text>
    </view>
    
    <!-- 下载按钮 -->
    <view class="action-btn download-btn" wx:if="{{converted}}" bindtap="onDownload">
      <text class="btn-text">下载Word文档</text>
    </view>
    
    <!-- 重新选择按钮 -->
    <view class="action-btn secondary-btn" wx:if="{{converted}}" bindtap="onSelectNewFile">
      <text class="btn-text">转换新文件</text>
    </view>
  </view>

</view>
