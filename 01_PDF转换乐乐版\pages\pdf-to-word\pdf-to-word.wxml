<!--pages/pdf-to-word/pdf-to-word.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF转Word</view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectFile">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/convert.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择或拖拽文件到此处</view>
        <view class="upload-tips">支持PDF格式，转换为Word文档</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !converting}}">
    <!-- 多文件显示 -->
    <view wx:if="{{selectedFiles.length > 1}}">
      <view class="file-info-card" wx:for="{{selectedFiles}}" wx:key="id">
        <image class="file-icon" src="/images/pdf-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{item.name}}</view>
          <view class="file-size-info">
            <text class="size-label">原文件：</text>
            <text class="size-value">{{item.size}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">转换后：</text>
            <text class="size-value {{item.convertedSize ? 'converted' : 'placeholder'}}">{{item.convertedSize || 'XXX KB'}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>

    <!-- 单文件显示 -->
    <view wx:if="{{selectedFiles.length <= 1}}">
      <view class="file-info-card">
        <image class="file-icon" src="/images/pdf-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{fileName}}</view>
          <view class="file-size-info">
            <text class="size-label">原文件：</text>
            <text class="size-value">{{fileSize}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">转换后：</text>
            <text class="size-value {{convertedSize ? 'converted' : 'placeholder'}}">{{convertedSize || 'XXX KB'}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>

  </view>

  <!-- 底部固定区域 -->
  <view class="footer-area" wx:if="{{fileSelected && !converting && !converted}}">
    <!-- 转换设置 -->
    <view class="convert-settings">
      <!-- 转换质量滑块 -->
      <view class="setting-item">
        <view class="setting-header">
          <text class="setting-label">转换质量</text>
          <text class="setting-value">{{qualityText}}</text>
        </view>
        <view class="slider-container">
          <slider
            value="{{qualityLevel}}"
            min="1"
            max="3"
            step="1"
            show-value="false"
            bindchange="onQualityLevelChange"
            activeColor="#4A90E2"
            backgroundColor="rgba(74, 144, 226, 0.1)"
            block-color="#ffffff"
            block-size="28"
          />
          <view class="slider-tips">
            <text class="tip-text">快速</text>
            <text class="tip-text">标准</text>
            <text class="tip-text">高质量</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 转换进行中 -->
  <view class="converting-section" wx:if="{{converting}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/convert.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在转换为Word...</view>
        <view class="progress-subtitle">请稍候，正在为您转换文档格式</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 转换完成 -->
  <view class="result-section" wx:if="{{converted}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/document.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">转换完成！</view>
        <view class="result-subtitle">PDF已成功转换为Word文档</view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onConvertAnother">
            <text>转换其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadWord">
            <text>下载Word文档</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule">
    <view class="capsule-btn left" bindtap="onSelectPDF">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{!converted ? 'disabled' : ''}}" bindtap="onDownloadWord">
      <view class="capsule-text">下载Word</view>
    </view>
  </view>

</view>
