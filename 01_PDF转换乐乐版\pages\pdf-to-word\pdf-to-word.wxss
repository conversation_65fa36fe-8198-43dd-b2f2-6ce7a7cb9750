/* pages/pdf-to-word/pdf-to-word.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(74, 144, 226, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(74, 144, 226, 0.3),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(74, 144, 226, 0.2),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    6rpx 6rpx 12rpx rgba(74, 144, 226, 0.2),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-svg {
  width: 64rpx;
  height: 64rpx;
}

.upload-content {
  width: 100%;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.upload-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
}

.upload-tips {
  font-size: 24rpx;
  color: #4A90E2;
  background: rgba(74, 144, 226, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 文件信息卡片 */
.file-info-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  box-shadow:
    8rpx 8rpx 16rpx rgba(74, 144, 226, 0.15),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.file-info-card:active {
  transform: scale(0.98);
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 16rpx;
  background: rgba(74, 144, 226, 0.1);
  padding: 16rpx;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.size-label {
  color: #666666;
}

.size-value {
  color: #1a1a1a;
  font-weight: 500;
}

.size-value.converted {
  color: #4A90E2;
  font-weight: 600;
}

.size-value.placeholder {
  color: #cccccc;
}

.size-separator {
  color: #e0e0e0;
}

.remove-btn {
  background: rgba(74, 144, 226, 0.1);
  color: #4A90E2;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.remove-btn:active {
  background: rgba(74, 144, 226, 0.2);
  transform: scale(0.95);
}

/* 转换设置 */
.convert-settings {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(74, 144, 226, 0.15),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
}

.setting-item {
  margin-bottom: 32rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.setting-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.setting-value {
  font-size: 28rpx;
  color: #4A90E2;
  font-weight: 500;
}



/* 滑块容器 */
.slider-container {
  padding: 30rpx 20rpx;
  border-radius: 20rpx;
  background: #f0f2f5;
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(74, 144, 226, 0.2),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.slider-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #5a6c7d;
}

.tip-text {
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 开始转换按钮 */
.start-convert-btn {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 32rpx;
  text-align: center;
  box-shadow:
    8rpx 8rpx 16rpx rgba(74, 144, 226, 0.2),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.start-convert-btn:active {
  transform: scale(0.98);
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(74, 144, 226, 0.3),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.convert-btn-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
}

/* 转换进行中 */
.converting-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.progress-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 64rpx 48rpx;
  text-align: center;
  width: 100%;
  max-width: 600rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(74, 144, 226, 0.2),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8);
}

.progress-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 32rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.progress-svg {
  width: 64rpx;
  height: 64rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.progress-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #4A90E2;
}

/* 转换完成 */
.result-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.result-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 64rpx 48rpx;
  text-align: center;
  width: 100%;
  max-width: 600rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(74, 144, 226, 0.2),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8);
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 32rpx;
  background: rgba(52, 199, 89, 0.1);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-svg {
  width: 64rpx;
  height: 64rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.result-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

.result-info {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 16rpx;
}

.info-label {
  color: #666666;
}

.info-value {
  color: #1a1a1a;
  font-weight: 500;
}

.info-separator {
  color: #e0e0e0;
  margin: 0 16rpx;
}

/* 结果页面按钮 */
.result-actions {
  display: flex;
  gap: 36rpx;
  z-index: 1000;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 1;
  height: 80rpx;
}

.action-btn:active {
  transform: scale(0.96);
}

.action-btn.primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(74, 144, 226, 0.3),
    0 2rpx 6rpx rgba(74, 144, 226, 0.2);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666666;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.1),
    0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 转换质量面板 */
.quality-panel {
  position: fixed;
  bottom: 160rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 600rpx;
  z-index: 999;
}

/* 开始转换按钮 */
.start-convert-btn {
  margin-top: 32rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow:
    0 8rpx 24rpx rgba(74, 144, 226, 0.3),
    0 4rpx 8rpx rgba(74, 144, 226, 0.2);
  transition: all 0.3s ease;
}

.start-convert-btn:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 12rpx rgba(74, 144, 226, 0.2),
    0 2rpx 4rpx rgba(74, 144, 226, 0.1);
}

.convert-btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 底部胶囊按钮 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(74, 144, 226, 0.3),
    0 2rpx 6rpx rgba(74, 144, 226, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.capsule-text {
  font-size: 39rpx;
  font-weight: 500;
  line-height: 1;
}
